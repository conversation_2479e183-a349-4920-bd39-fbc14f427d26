**Coursework on Search Strategy**

**This coursework is worth 15% of the module assessment. You should submit a 3-page report with Microsoft Excel spreadsheet to show your calculation.** Late submissions (without acceptable extenuating circumstances) will receive a mark of *zero*.

A traveling salesman has to travel through a bunch of cities, in such a way that the expenses on traveling are minimized. This assignment is related to finding to the route that starts at city 1, then visits each other city exactly once and then ends up in city 1 such that the total distance is minimised. 

| Cities | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| 1 | 0 |  |  |  |  |  |  |  |  |  |
| 2 | A | 0 |  |  |  |  |  |  |  |  |
| 3 | 10 | 23 | 0 |  |  |  |  |  |  |  |
| 4 | 12 | 45 | 34 | 0 |  |  |  |  |  |  |
| 5 | 5 | B | 22 | 12 | 0 |  |  |  |  |  |
| 6 | 4 | 45 | 12 | 13 | 25 | 0 |  |  |  |  |
| 7 | 19 | 24 | C | 26 | 21 | 11 | 0 |  |  |  |
| 8 | 14 | 9 | 23 | 43 | 5 | 22 | 20 | 0 |  |  |
| 9 | 3 | 34 | 22 | 33 | 7 | 12 | 33 | 6 | 0 |  |
| 10 | 18 | 12 | 21 | 21 | 22 | 10 | 11 | 23 | 17 | 0 |

Notes  
•	Use A \= (10 \+ 2 \* your 3rd last digit in your student ID).  
•	Use B \= (27 \+ your 2nd last student ID digit).   
•	Use C \= (5 \+ 3\*your last student ID digit). 

\[E.g. if your student ID is B00012345 use A \= (10+ 2\*3) \= 16; B \= (27+4) \= 31 and C \= (5 \+ 3\*5) \= 20\].

\[E.g. if your student ID is B45678900 use A \= (10 \+ 2 \*9) \= 28; B \= (27+0) \= 27 and C \= (5 \+ 0\) \= 5\].

Develop and analyse TSP routes using the excel model used in your lab session using the following algorithms. Along with your excel model, you should also submit a brief report answering the following tasks (maximum 3 pages including all diagrams).

1) #### Derive correct parameters for the TSP problem using your Student numbers, describe the problem briefly and develop excel model. You can use the lab excel spreadsheet for adapting to your problem as required.

   \[20%\]

2) #### Construct an initial route using **the nearest neighbour (NN)** algorithm as the construction algorithm. Show the path costs at each stage of the solution development. Also show the constructed complete solution in a diagram in the report. Implement it as the initial solution in the lab spreadsheet. 

\[30%\]

3) With the initial route constructed in 1), demonstrate fifty further iterations using any iterative improvement technique of your choice implementing it in an excel spreadsheet. You can use the lab spreadsheet for modifying as required.   
   

\[30%\]

4) Discuss the implementation of the chosen improvement technique in 3\) and comment on improvement (if any) over the iterations with a graph showing distance against iterations in the report. 

\[20%\]